/**
 * Server-side search data access layer
 * 
 * This module provides server-side functions for search functionality
 * including product search, suggestions, and filtering.
 */

import { createCacheableSupabaseClient } from '@/lib/supabase/server'
import { createCachedFunction, cacheKeys, CACHE_DURATIONS, CACHE_TAGS } from '@/lib/cache'
import type {
  TransformedProduct,
  SearchFilters,
  SearchResult,
  PaginatedResponse,
} from './types'

/**
 * Parse price from specification string (e.g., "£329.00" -> 329.00)
 */
function parseSpecificationPrice(priceString: string | null | undefined): number | null {
  if (!priceString || typeof priceString !== 'string') return null;

  // Remove currency symbols, commas, and whitespace, then parse
  const cleanPrice = priceString.replace(/[£$€,\s]/g, '');
  const parsed = parseFloat(cleanPrice);

  return isNaN(parsed) ? null : parsed;
}

/**
 * Transform raw product data for search results
 */
function transformSearchProduct(rawProduct: any): TransformedProduct {
  // Calculate minPrice from retailer offers or specifications
  const retailerOffers = (rawProduct.product_retailer_offers || []);
  const retailerPrices = retailerOffers.map((offer: any) => offer.price).filter((price: any) => price != null);
  const specificationPrice = parseSpecificationPrice(rawProduct.specifications?.price);

  let minPrice: number | null = null;
  if (retailerPrices.length > 0) {
    minPrice = Math.min(...retailerPrices);
  } else if (specificationPrice !== null) {
    minPrice = specificationPrice;
  }

  return {
    id: rawProduct.id,
    name: rawProduct.name,
    slug: rawProduct.slug,
    description: rawProduct.description || '',
    images: rawProduct.images || [],
    specifications: rawProduct.specifications || null,
    status: rawProduct.status || 'active',
    isFeatured: rawProduct.is_featured || false,
    isSponsored: rawProduct.is_sponsored || false,
    cashbackAmount: rawProduct.cashback_amount,
    minPrice: minPrice,
    modelNumber: rawProduct.model_number || '',
    createdAt: rawProduct.created_at,
    updatedAt: rawProduct.updated_at,
    brand: rawProduct.brand || null,
    category: rawProduct.category || null,
    promotion: rawProduct.promotion || null,
    retailerOffers: (rawProduct.product_retailer_offers || []).map((offer: any) => ({
      id: offer.id,
      retailer: {
        id: offer.retailer?.id || '',
        name: offer.retailer?.name || '',
        logoUrl: offer.retailer?.logo_url || null,
        websiteUrl: offer.retailer?.website_url || null,
      },
      price: offer.price,
      stockStatus: offer.stock_status || 'unknown',
      url: offer.url || null,
      createdAt: offer.created_at,
    })),
  }
}

/**
 * Search products with advanced filtering and sorting
 */
async function _searchProducts(
  filters: SearchFilters,
  page = 1,
  limit = 20
): Promise<SearchResult> {
  try {
    const supabase = createCacheableSupabaseClient()
    const offset = (page - 1) * limit

    let query = supabase
      .from('products')
      .select(`
        *,
        brand:brand_id (
          id,
          name,
          slug,
          logo_url
        ),
        category:category_id (
          id,
          name,
          slug
        ),
        promotion:promotion_id (
          id,
          title,
          max_cashback_amount
        ),
        product_retailer_offers (
          id,
          price,
          stock_status,
          url,
          retailer:retailer_id (
            id,
            name,
            logo_url
          )
        )
      `, { count: 'exact' })

    // Apply search query
    if (filters.query && filters.query.trim()) {
      const searchTerm = filters.query.trim()
      query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
    }

    // Apply filters
    if (filters.brand) {
      query = query.eq('brand_id', filters.brand)
    }
    if (filters.category) {
      query = query.eq('category_id', filters.category)
    }
    if (filters.minPrice !== undefined) {
      // This would require a join with product_retailer_offers
      // For now, we'll implement basic filtering
    }
    if (filters.maxPrice !== undefined) {
      // This would require a join with product_retailer_offers
      // For now, we'll implement basic filtering
    }

    // Always filter for active products
    query = query.eq('status', 'active')

    // Apply sorting
    switch (filters.sortBy) {
      case 'price_asc':
        // Would need to join with offers for price sorting
        query = query.order('name', { ascending: true })
        break
      case 'price_desc':
        // Would need to join with offers for price sorting
        query = query.order('name', { ascending: false })
        break
      case 'newest':
        query = query.order('created_at', { ascending: false })
        break
      case 'featured':
        query = query.order('is_featured', { ascending: false })
          .order('created_at', { ascending: false })
        break
      case 'relevance':
      default:
        // For text search, order by relevance (simplified)
        if (filters.query) {
          query = query.order('is_featured', { ascending: false })
            .order('created_at', { ascending: false })
        } else {
          query = query.order('created_at', { ascending: false })
        }
        break
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1)

    const { data, error, count } = await query

    if (error) {
      console.error('Error in search query:', error)
      throw new Error(`Search failed: ${error.message}`)
    }

    const transformedProducts = (data || []).map(transformSearchProduct)
    const total = count || 0

    return {
      products: transformedProducts,
      total,
      filtersApplied: filters,
      suggestions: [], // TODO: Implement search suggestions
    }
  } catch (error) {
    console.error('Exception in searchProducts:', error)
    throw error
  }
}

/**
 * Cached version of searchProducts
 */
export const searchProducts = createCachedFunction(
  _searchProducts,
  {
    key: 'searchProducts',
    revalidate: CACHE_DURATIONS.SHORT,
    tags: [CACHE_TAGS.SEARCH, CACHE_TAGS.PRODUCTS],
  }
)

/**
 * Get search suggestions based on query
 */
async function _getSearchSuggestions(query: string, limit = 5): Promise<string[]> {
  try {
    if (!query || query.trim().length < 2) {
      return []
    }

    const supabase = createCacheableSupabaseClient()
    const searchTerm = query.trim()

    // Get product name suggestions
    const { data: productSuggestions, error: productError } = await supabase
      .from('products')
      .select('name')
      .ilike('name', `%${searchTerm}%`)
      .eq('status', 'active')
      .limit(limit)

    if (productError) {
      console.error('Error fetching product suggestions:', productError)
    }

    // Get brand name suggestions
    const { data: brandSuggestions, error: brandError } = await supabase
      .from('brands')
      .select('name')
      .ilike('name', `%${searchTerm}%`)
      .limit(limit)

    if (brandError) {
      console.error('Error fetching brand suggestions:', brandError)
    }

    // Combine and deduplicate suggestions
    const allSuggestions = [
      ...(productSuggestions || []).map((p: { name: string }) => p.name),
      ...(brandSuggestions || []).map((b: { name: string }) => b.name),
    ]

    // Remove duplicates and limit results
    const uniqueSuggestions = Array.from(new Set(allSuggestions))
      .slice(0, limit)

    return uniqueSuggestions
  } catch (error) {
    console.error('Exception in getSearchSuggestions:', error)
    return []
  }
}

/**
 * Cached version of getSearchSuggestions
 */
export const getSearchSuggestions = createCachedFunction(
  _getSearchSuggestions,
  {
    key: 'getSearchSuggestions',
    revalidate: CACHE_DURATIONS.MEDIUM,
    tags: [CACHE_TAGS.SEARCH],
  }
)

/**
 * Get popular search terms (placeholder implementation)
 */
async function _getPopularSearchTerms(limit = 10): Promise<string[]> {
  // TODO: Implement based on search analytics
  // For now, return some common terms
  return [
    'iPhone',
    'Samsung',
    'Laptop',
    'Headphones',
    'Gaming',
    'Fitness',
    'Home',
    'Fashion',
    'Beauty',
    'Electronics',
  ].slice(0, limit)
}

/**
 * Cached version of getPopularSearchTerms
 */
export const getPopularSearchTerms = createCachedFunction(
  _getPopularSearchTerms,
  {
    key: 'getPopularSearchTerms',
    revalidate: CACHE_DURATIONS.EXTENDED,
    tags: [CACHE_TAGS.SEARCH],
  }
)
