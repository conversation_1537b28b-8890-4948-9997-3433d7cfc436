// src/app/search/page.tsx - Search page with Server-Side Rendering
// Optimized for SEO with dynamic metadata and structured data

import { Suspense } from 'react';
import { constructMetadata } from '@/lib/metadata-utils';
import { searchProducts } from '@/lib/data/products';
import { SearchPageClient } from '@/components/pages/SearchPageClient';
import { SearchResultsStructuredData } from '@/components/seo/StructuredData';

interface SearchPageProps {
    searchParams: Promise<{
        q?: string;
        page?: string;
        category?: string;
        subcategory?: string;
    }>;
}

// Generate dynamic metadata for SEO optimization
export async function generateMetadata({ searchParams }: SearchPageProps) {
    const params = await searchParams;
    const query = params.q || '';
    const category = params.category || '';
    const subcategory = params.subcategory || '';

    if (!query && !category && !subcategory) {
        return constructMetadata({
            title: 'Search Products - Find Cashback Deals',
            description: 'Search for products and discover cashback deals from top brands. Find the best offers and save money on your purchases.',
            pathname: '/search'
        });
    }

    // Create SEO-optimized metadata for search queries
    let title = 'Search Results';
    let description = 'Find cashback deals and offers';

    if (query) {
        title = `"${query}" - Search Results`;
        description = `Search results for "${query}". Find cashback deals and exclusive offers on ${query} from top retailers.`;
    }

    if (category) {
        const categoryText = subcategory ? `${category} > ${subcategory}` : category;
        title = `${categoryText} - Cashback Deals`;
        description = `Discover cashback deals and offers in ${categoryText}. Save money on your purchases with exclusive rebates and rewards.`;
    }

    const keywords = [
        query,
        category,
        subcategory,
        'cashback',
        'deals',
        'offers',
        'search',
        'products',
        'save money'
    ].filter(Boolean).join(', ');

    return constructMetadata({
        title,
        description,
        pathname: `/search${query ? `?q=${encodeURIComponent(query)}` : ''}`
    });
}

// Loading skeleton component for better UX during data fetching
function SearchPageSkeleton() {
    return (
        <div className="container py-12">
            {/* Header skeleton */}
            <div className="mb-8">
                <div className="h-10 bg-gray-300 rounded w-96 mb-4 animate-pulse"></div>
                <div className="h-6 bg-gray-300 rounded w-64 animate-pulse"></div>
            </div>

            {/* Search bar skeleton */}
            <div className="max-w-2xl mx-auto mb-8">
                <div className="h-16 bg-gray-300 rounded-lg animate-pulse"></div>
            </div>

            {/* Controls skeleton */}
            <div className="flex justify-between items-center mb-8">
                <div className="flex items-center gap-4">
                    <div className="h-6 bg-gray-300 rounded w-32 animate-pulse"></div>
                    <div className="h-10 bg-gray-300 rounded w-24 animate-pulse"></div>
                </div>
                <div className="h-10 bg-gray-300 rounded w-40 animate-pulse"></div>
            </div>

            {/* Results skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {Array.from({ length: 12 }).map((_, i) => (
                    <div key={i} className="h-80 bg-gray-300 rounded animate-pulse"></div>
                ))}
            </div>
        </div>
    );
}

// Main search page component - Server Component for optimal SEO
export default async function SearchPage({ searchParams }: SearchPageProps) {
    try {
        const params = await searchParams;
        // Parse search parameters
        const query = params.q || '';
        const page = parseInt(params.page || '1', 10);
        const category = params.category || '';
        const subcategory = params.subcategory || '';

        // Server-side search data fetching for improved SEO and Core Web Vitals
        const filters = {
            ...(query && { search: query }),
            ...(category && { category }),
            ...(subcategory && { subcategory }),
        };

        const searchResults = await searchProducts(query); // Pass query string as per function signature

        return (
            <>
                {/* SearchResultsPage structured data for enhanced SEO */}
                {query && searchResults.length > 0 && (
                    <SearchResultsStructuredData
                        query={query}
                        results={searchResults}
                        totalResults={searchResults.length}
                    />
                )}

                {/* Suspense boundary for progressive loading */}
                <Suspense fallback={<SearchPageSkeleton />}>
                    <SearchPageClient
                        products={searchResults}
                        totalCount={searchResults.length}
                        currentPage={page}
                        searchQuery={query}
                        category={category}
                        subcategory={subcategory}
                    />
                </Suspense>
            </>
        );
    } catch (error) {
        console.error('Error loading search page:', error);

        // Fallback content in case of data fetching errors
        return (
            <div className="container py-20 text-center">
                <h1 className="text-4xl font-bold text-primary mb-6">
                    Search Products
                </h1>
                <p className="text-lg text-foreground/70 mb-8">
                    Find cashback deals and exclusive offers from top brands.
                </p>
                <p className="text-foreground/60">
                    We're currently loading search results. Please refresh the page or try again later.
                </p>
            </div>
        );
    }
}
