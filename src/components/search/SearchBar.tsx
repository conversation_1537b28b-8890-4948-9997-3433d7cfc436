'use client'

import { useState, useEffect, useRef } from 'react'
import { Search as SearchIcon } from 'lucide-react'
import { useDebounce } from '../../hooks/useDebounce'
import { SearchSuggestions } from './SearchSuggestions'

interface Suggestion {
  id: string
  name: string
}

interface SearchBarProps {
  onSearch?: (query: string) => void
  suggestions?: Suggestion[]
  isLoading?: boolean
  error?: string | null
  isOpen?: boolean
  onOpenChange?: (isOpen: boolean) => void
  inHeader?: boolean
}

export function SearchBar({ 
  onSearch, 
  suggestions, 
  isLoading = false, 
  error = null, 
  isOpen, 
  onOpenChange 
}: SearchBarProps) {
  const [query, setQuery] = useState('')
  const [internalIsOpen, setIsOpen] = useState(false)
  const isOpenControlled = isOpen !== undefined ? isOpen : internalIsOpen
  
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open)
    onOpenChange?.(open)
  }
  const searchRef = useRef<HTMLDivElement>(null)

  const debouncedQuery = useDebounce(query, 300)

  useEffect(() => {
    onSearch?.(debouncedQuery)
  }, [debouncedQuery, onSearch])

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className="relative w-full max-w-md" ref={searchRef}>
      <div className="flex items-center border rounded-lg overflow-hidden">
        <input
          type="text"
          placeholder="Search products..."
          className="w-full px-4 py-2 focus:outline-none"
          value={query}
          onChange={(e) => {
            setQuery(e.target.value)
            handleOpenChange(true)
          }}
          onFocus={() => handleOpenChange(true)}
        />
        <button
          className="px-4 py-2 bg-primary text-white hover:bg-primary/90"
          onClick={() => {
            onSearch?.(query)
            handleOpenChange(false)
          }}
        >
          <SearchIcon className="h-5 w-5" />
        </button>
      </div>

      {isOpenControlled && query.length > 0 && (
        <SearchSuggestions query={query} />
      )}
    </div>
  )
}
