'use client';

import { useEffect, useState } from 'react';
import { useDebounce } from '@/hooks/useDebounce';

interface Suggestion {
  name: string;
  type: 'category' | 'brand' | 'product';
}

export function SearchSuggestions({ query }: { query: string }) {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const debouncedQuery = useDebounce(query, 300);

  useEffect(() => {
    if (debouncedQuery.length > 2) {
      setIsLoading(true);
      fetch(`/api/search/suggestions?q=${encodeURIComponent(debouncedQuery)}`)
        .then(res => res.json())
        .then(data => {
          const formattedSuggestions = [
            ...data.categories.map((c: any) => ({ ...c, type: 'category' })),
            ...data.brands.map((b: any) => ({ ...b, type: 'brand' })),
            ...data.products.map((p: any) => ({ ...p, type: 'product' }))
          ];
          setSuggestions(formattedSuggestions.slice(0, 6));
        })
        .finally(() => setIsLoading(false));
    } else {
      setSuggestions([]);
    }
  }, [debouncedQuery]);

  if (!query || query.length < 3) return null;

  return (
    <div className="absolute top-full mt-2 w-full bg-white shadow-lg rounded-lg overflow-hidden z-50">
      {isLoading ? (
        <div className="p-4 text-gray-500">Loading suggestions...</div>
      ) : suggestions.length > 0 ? (
        <ul>
          {suggestions.map((suggestion, index) => (
            <li key={index} className="hover:bg-gray-100 transition-colors">
              <a
                href={`/search?q=${encodeURIComponent(suggestion.name)}`}
                className="block px-4 py-2 text-gray-700"
              >
                <span className="capitalize">{suggestion.type}: </span>
                {suggestion.name}
              </a>
            </li>
          ))}
        </ul>
      ) : (
        <div className="p-4 text-gray-500">No suggestions found</div>
      )}
    </div>
  );
}
