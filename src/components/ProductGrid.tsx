import React from 'react'
import { motion } from 'framer-motion'

interface ProductGridProps {
	children: React.ReactNode
}

export function ProductGrid({ children }: ProductGridProps) {
	return (
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
			{React.Children.map(children, (child) => (
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					whileHover={{ y: -5 }}
				>
					{child}
				</motion.div>
			))}
		</div>
	)
}