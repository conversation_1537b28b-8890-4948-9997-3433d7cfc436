{"folders": [{"path": "."}], "settings": {"typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "json.schemas": [{"fileMatch": ["package.json"], "url": "https://json.schemastore.org/package.json"}], "liveServer.settings.port": 5502, "aide.enableAgentReasoning": true, "aide.enableInspectWithDevtools": true, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/CVS": true, "**/.retool_types/**": true, "**/*tsconfig.json": true, ".cache": true, "retool.config.json": true}}}