import { getSimilarProducts } from '@/lib/data/products';
import { createCacheableSupabaseClient } from '@/lib/supabase/server';

jest.mock('@/lib/supabase/server', () => ({
  __esModule: true,
  createCacheableSupabaseClient: jest.fn(),
}));

describe('getSimilarProducts', () => {
  const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    then: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (createCacheableSupabaseClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  it('should fetch similar products filtering by category_id and excluding the product id', async () => {
    const mockData = [
      { id: '2', name: 'Product 2', category_id: 'cat1' },
      { id: '3', name: 'Product 3', category_id: 'cat1' },
    ];
    mockSupabase.then.mockImplementation((resolve) => resolve(mockData));

    // We need to adjust getSimilarProducts to accept category_id parameter for this test
    // For now, we test that the query methods are called correctly
    await getSimilarProducts('cat1', '1');

    expect(mockSupabase.from).toHaveBeenCalledWith('products');
    expect(mockSupabase.select).toHaveBeenCalledWith('*, brand:brand_id (id name slug logo_url description), promotion:promotion_id (id title description max_cashback_amount purchase_start_date purchase_end_date status is_featured), retailer_offers:product_retailer_offers(*)');
    expect(mockSupabase.eq).toHaveBeenCalledWith('category_id', 'cat1');
    expect(mockSupabase.neq).toHaveBeenCalledWith('id', '1');
    expect(mockSupabase.limit).toHaveBeenCalledWith(10);
  });

  it('should return empty array on error', async () => {
    mockSupabase.then.mockImplementation(() => { throw new Error('DB error'); });

    const result = await getSimilarProducts('cat1', '1');
    expect(result).toEqual([]);
  });
});

// Additional tests for centralized error handling can be added here once implemented
