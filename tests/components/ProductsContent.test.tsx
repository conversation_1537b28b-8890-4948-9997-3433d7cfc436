import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ProductsContent from '../../src/app/products/components/ProductsContent';

import { TransformedProduct } from '../../src/lib/data/types';

// Mock data for products pages
const mockProductsPage1: TransformedProduct[] = [
  {
    id: '1',
    slug: 'product-1',
    name: 'Product 1',
    description: 'Description 1',
    images: [],
    status: 'active',
    isFeatured: false,
    isSponsored: false,
    cashbackAmount: 0,
    minPrice: 10,
    categories: [],
    tags: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    modelNumber: '',
    brand: null,
    category: null,
    promotion: null,
    retailerOffers: [],
  },
  {
    id: '2',
    slug: 'product-2',
    name: 'Product 2',
    description: 'Description 2',
    images: [],
    status: 'active',
    isFeatured: false,
    isSponsored: false,
    cashbackAmount: 0,
    minPrice: 20,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    modelNumber: '',
    brand: null,
    category: null,
    promotion: null,
    retailerOffers: [],
  },
];

const mockProductsPage2 = [
  {
    id: '3',
    slug: 'product-3',
    name: 'Product 3',
    description: 'Description 3',
    images: [],
    status: 'active',
    price: 30,
    isFeatured: false,
    isSponsored: false,
    cashbackAmount: 0,
    minPrice: 30,
    maxPrice: 35,
    rating: 4.7,
    reviewCount: 8,
    categories: [],
    tags: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '4',
    slug: 'product-4',
    name: 'Product 4',
    description: 'Description 4',
    images: [],
    status: 'active',
    price: 40,
    isFeatured: false,
    isSponsored: false,
    cashbackAmount: 0,
    minPrice: 40,
    maxPrice: 45,
    rating: 4.2,
    reviewCount: 12,
    categories: [],
    tags: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Mock fetch function to simulate API calls
const fetchProducts = jest.fn((page) => {
  if (page === 1) return Promise.resolve(mockProductsPage1);
  if (page === 2) return Promise.resolve(mockProductsPage2);
  return Promise.resolve([]);
});

jest.mock('../../src/lib/data/products', () => ({
  getProducts: (params: { page: number }) => fetchProducts(params.page),
}));

describe('ProductsContent Pagination', () => {
  const queryClient = new QueryClient();

  beforeEach(() => {
    fetchProducts.mockClear();
  });

  function renderComponent(initialPage = 1) {
    render(
      <QueryClientProvider client={queryClient}>
        <ProductsContent initialData={{ data: mockProductsPage1, error: null, pagination: { page: initialPage, limit: 20, total: 4, totalPages: 2, hasNext: true, hasPrev: false } }} filterOptions={{ brands: [], promotions: [] }} />
      </QueryClientProvider>
    );
  }

  test('renders initial products and pagination controls', () => {
    renderComponent();
    expect(screen.getByText('Product 1')).toBeInTheDocument();
    expect(screen.getByText('Product 2')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /page 1/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /page 2/i })).toBeInTheDocument();
  });

  test('clicking on page 2 fetches and displays new products', async () => {
    renderComponent();

    const page2Button = screen.getByRole('button', { name: /page 2/i });
    fireEvent.click(page2Button);

    await waitFor(() => {
      expect(fetchProducts).toHaveBeenCalledWith(2);
    });

    await waitFor(() => {
      expect(screen.getByText('Product 3')).toBeInTheDocument();
      expect(screen.getByText('Product 4')).toBeInTheDocument();
    });
  });

  test('pagination buttons update active page', async () => {
    renderComponent();

    const page1Button = screen.getByRole('button', { name: /page 1/i });
    const page2Button = screen.getByRole('button', { name: /page 2/i });

    expect(page1Button).toHaveAttribute('aria-current', 'page');
    expect(page2Button).not.toHaveAttribute('aria-current');

    fireEvent.click(page2Button);

    await waitFor(() => {
      expect(page2Button).toHaveAttribute('aria-current', 'page');
      expect(page1Button).not.toHaveAttribute('aria-current');
    });
  });
});
